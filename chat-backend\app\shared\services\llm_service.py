"""
Centralized LLM service for managing OpenAI clients.
"""

import os
from typing import Optional
from langchain_openai import ChatOpenAI
from openai import AsyncOpenAI

from ..config import MODEL_CONFIG


class LLMService:
    """Centralized service for managing LLM clients."""
    
    _instance: Optional['LLMService'] = None
    
    def __new__(cls) -> 'LLMService':
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        if hasattr(self, '_initialized'):
            return
            
        self.api_key = os.getenv("OPENAI_API_KEY")
        if not self.api_key:
            raise ValueError("OPENAI_API_KEY environment variable is not set")
        
        self._initialized = True
    
    def get_fast_llm(self, streaming: bool = False) -> ChatOpenAI:
        """Get fast LLM client (gpt-4o-mini)."""
        return ChatOpenAI(
            model=MODEL_CONFIG["FAST_MODEL"],
            temperature=MODEL_CONFIG["TEMPERATURE"],
            streaming=streaming,
            api_key=self.api_key
        )
    
    def get_quality_llm(self, streaming: bool = False) -> ChatOpenAI:
        """Get quality LLM client (gpt-4o)."""
        return ChatOpenAI(
            model=MODEL_CONFIG["QUALITY_MODEL"],
            temperature=MODEL_CONFIG["TEMPERATURE"],
            streaming=streaming,
            api_key=self.api_key
        )
    
    def get_chat_llm(self, streaming: bool = False) -> ChatOpenAI:
        """Get chat LLM client (gpt-3.5-turbo)."""
        return ChatOpenAI(
            model=MODEL_CONFIG["SIMPLE_CHAT_MODEL"],
            temperature=MODEL_CONFIG["CHAT_TEMPERATURE"],
            streaming=streaming,
            api_key=self.api_key
        )
    
    def get_async_openai_client(self) -> AsyncOpenAI:
        """Get async OpenAI client for direct API calls."""
        return AsyncOpenAI(api_key=self.api_key)
