"""
Advanced LangGraph Search Engine - Python implementation.
Based on the TypeScript langgraph-search-engine.ts file.
Implements full advanced features with 6-phase processing.
"""

import asyncio
import json
import os
from datetime import datetime
from typing import Dict, List, Any, Optional, Callable, TypedDict, Literal, Union
from dataclasses import dataclass, field

from langgraph.graph import StateGraph, START, END
from langgraph.graph.state import CompiledStateGraph
from langgraph.checkpoint.memory import MemorySaver
from langchain_openai import ChatOpenAI
from langchain_core.messages import HumanMessage, SystemMessage
from tavily import TavilyClient

from .config import SEARCH_CONFIG, MODEL_CONFIG
from .context_processor import ContextProcessor, ProcessedSource
from .perplexity_client import PerplexityClient


SearchPhase = Literal[
    'understanding', 'planning', 'searching', 'analyzing', 'synthesizing', 'complete', 'error'
]

ErrorType = Literal['search', 'scrape', 'llm', 'unknown']


@dataclass
class SearchEvent:
    """Search event for progress tracking."""
    type: str
    phase: Optional[SearchPhase] = None
    message: Optional[str] = None
    query: Optional[str] = None
    index: Optional[int] = None
    total: Optional[int] = None
    sources: Optional[List[Dict[str, Any]]] = None
    url: Optional[str] = None
    title: Optional[str] = None
    stage: Optional[str] = None
    summary: Optional[str] = None
    chunk: Optional[str] = None
    content: Optional[str] = None
    follow_up_questions: Optional[List[str]] = None
    error: Optional[str] = None
    error_type: Optional[ErrorType] = None


@dataclass
class Source:
    """Source data structure."""
    url: str
    title: str
    content: Optional[str] = None
    quality: Optional[float] = None
    summary: Optional[str] = None


@dataclass
class SubQuery:
    """Sub-query with confidence tracking."""
    question: str
    search_query: str
    answered: bool = False
    confidence: float = 0.0
    sources: List[Source] = field(default_factory=list)


@dataclass
class SearchStep:
    """Search step for UI progress tracking."""
    id: str
    label: str
    status: Literal['pending', 'active', 'completed']
    start_time: Optional[int] = None


class SearchState(TypedDict, total=False):
    """LangGraph state with proper typing."""
    query: str
    context: Optional[List[Dict[str, str]]]
    
    understanding: Optional[str]
    search_queries: Optional[List[str]]
    current_search_index: int
    sub_queries: Optional[List[SubQuery]]
    search_attempt: int
    
    sources: List[Source]
    scraped_sources: List[Source]
    processed_sources: Optional[List[ProcessedSource]]
    final_answer: Optional[str]
    follow_up_questions: Optional[List[str]]
    
    phase: SearchPhase
    error: Optional[str]
    error_type: Optional[ErrorType]
    retry_count: int
    max_retries: int


class AdvancedSearchEngine:
    """
    Advanced LangGraph-based search engine with 6-phase processing:
    1. Understanding - Analyze the user query
    2. Planning - Generate search strategies and sub-queries
    3. Searching - Execute searches with multiple providers
    4. Scraping - Gather additional content from sources
    5. Analyzing - Process and score content relevance
    6. Synthesizing - Generate comprehensive streaming answer
    """
    
    def __init__(self, options: Optional[Dict[str, Any]] = None):
        """Initialize the advanced search engine."""
        options = options or {}
        
        self.tavily_client = TavilyClient(api_key=os.getenv("TAVILY_API_KEY"))
        self.perplexity_client = PerplexityClient(
            api_key=os.getenv("PERPLEXITY_API_KEY")
        )
        self.context_processor = ContextProcessor()
        
        openai_api_key = os.getenv("OPENAI_API_KEY")
        if not openai_api_key:
            raise ValueError("OPENAI_API_KEY environment variable is not set")
        
        self.llm = ChatOpenAI(
            model=MODEL_CONFIG["FAST_MODEL"],
            temperature=MODEL_CONFIG["TEMPERATURE"],
            api_key=openai_api_key
        )
        
        self.streaming_llm = ChatOpenAI(
            model=MODEL_CONFIG["QUALITY_MODEL"],
            temperature=MODEL_CONFIG["TEMPERATURE"],
            streaming=True,
            api_key=openai_api_key
        )
        
        self.checkpointer = None
        if options.get("enable_checkpointing"):
            self.checkpointer = MemorySaver()
        
        self.graph = self._build_graph()
    
    def get_initial_steps(self) -> List[SearchStep]:
        """Get initial search steps for UI progress tracking."""
        return [
            SearchStep(id='understanding', label='Understanding request', status='pending'),
            SearchStep(id='planning', label='Planning search', status='pending'),
            SearchStep(id='searching', label='Searching sources', status='pending'),
            SearchStep(id='analyzing', label='Analyzing content', status='pending'),
            SearchStep(id='synthesizing', label='Synthesizing answer', status='pending'),
            SearchStep(id='complete', label='Complete', status='pending')
        ]
    
    def _build_graph(self) -> CompiledStateGraph:
        """Build the LangGraph workflow."""
        workflow = StateGraph(SearchState)
        
        workflow.add_node("understand", self._understand_node)
        workflow.add_node("plan", self._plan_node)
        workflow.add_node("search", self._search_node)
        workflow.add_node("scrape", self._scrape_node)
        workflow.add_node("analyze", self._analyze_node)
        workflow.add_node("synthesize", self._synthesize_node)
        workflow.add_node("handle_error", self._handle_error_node)
        workflow.add_node("complete", self._complete_node)
        
        workflow.add_edge(START, "understand")
        
        workflow.add_conditional_edges(
            "understand",
            lambda state: "handle_error" if state.get("phase") == "error" else "plan",
            {"handle_error": "handle_error", "plan": "plan"}
        )
        
        workflow.add_conditional_edges(
            "plan",
            lambda state: "handle_error" if state.get("phase") == "error" else "search",
            {"handle_error": "handle_error", "search": "search"}
        )
        
        workflow.add_conditional_edges(
            "search",
            self._search_routing,
            {"handle_error": "handle_error", "search": "search", "scrape": "scrape"}
        )
        
        workflow.add_conditional_edges(
            "scrape",
            lambda state: "handle_error" if state.get("phase") == "error" else "analyze",
            {"handle_error": "handle_error", "analyze": "analyze"}
        )
        
        workflow.add_conditional_edges(
            "analyze",
            self._analyze_routing,
            {"handle_error": "handle_error", "plan": "plan", "synthesize": "synthesize"}
        )
        
        workflow.add_conditional_edges(
            "synthesize",
            lambda state: "handle_error" if state.get("phase") == "error" else "complete",
            {"handle_error": "handle_error", "complete": "complete"}
        )
        
        workflow.add_conditional_edges(
            "handle_error",
            self._error_routing,
            {"understand": "understand", "search": "search", "error": END}
        )
        
        workflow.add_edge("complete", END)
        
        return workflow.compile(checkpointer=self.checkpointer)
    
    def _search_routing(self, state: SearchState) -> str:
        """Route from search node based on state."""
        if state.get("phase") == "error":
            return "handle_error"
        
        current_index = state.get("current_search_index", 0)
        search_queries = state.get("search_queries", [])
        
        if current_index < len(search_queries):
            return "search"  # Continue searching
        return "scrape"  # Move to scraping
    
    def _analyze_routing(self, state: SearchState) -> str:
        """Route from analyze node based on state."""
        if state.get("phase") == "error":
            return "handle_error"
        elif state.get("phase") == "planning":
            return "plan"  # Retry with new search strategy
        else:
            return "synthesize"
    
    def _error_routing(self, state: SearchState) -> str:
        """Route from error handling node."""
        retry_count = state.get("retry_count", 0)
        max_retries = state.get("max_retries", SEARCH_CONFIG["MAX_RETRIES"])
        
        if retry_count < max_retries:
            error_type = state.get("error_type")
            return "search" if error_type == "search" else "understand"
        return "error"
    
    async def _understand_node(self, state: SearchState, config: Optional[Dict] = None) -> Dict[str, Any]:
        """Understanding phase - analyze the user query."""
        try:
            understanding = await self._analyze_query(state["query"], state.get("context"))
            
            return {
                "understanding": understanding,
                "phase": "planning"
            }
        except Exception as error:
            return {
                "error": str(error),
                "error_type": "llm",
                "phase": "error"
            }
    
    async def _plan_node(self, state: SearchState, config: Optional[Dict] = None) -> Dict[str, Any]:
        """Planning phase - generate search strategies."""
        try:
            sub_queries = state.get("sub_queries")
            if not sub_queries:
                extracted = await self._extract_sub_queries(state["query"])
                sub_queries = [
                    SubQuery(
                        question=sq["question"],
                        search_query=sq["searchQuery"],
                        answered=False,
                        confidence=0.0,
                        sources=[]
                    )
                    for sq in extracted
                ]
            
            unanswered_queries = [
                sq for sq in sub_queries 
                if not sq.answered or sq.confidence < SEARCH_CONFIG["MIN_ANSWER_CONFIDENCE"]
            ]
            
            if not unanswered_queries:
                return {
                    "sub_queries": sub_queries,
                    "phase": "analyzing"
                }
            
            search_attempt = state.get("search_attempt", 0)
            if search_attempt > 0:
                search_queries = await self._generate_alternative_search_queries(
                    sub_queries, search_attempt
                )
                
                alternative_index = 0
                for sq in sub_queries:
                    if not sq.answered or sq.confidence < SEARCH_CONFIG["MIN_ANSWER_CONFIDENCE"]:
                        if alternative_index < len(search_queries):
                            sq.search_query = search_queries[alternative_index]
                            alternative_index += 1
            else:
                search_queries = [sq.search_query for sq in unanswered_queries]
            
            return {
                "search_queries": search_queries,
                "sub_queries": sub_queries,
                "current_search_index": 0,
                "phase": "searching"
            }
        except Exception as error:
            return {
                "error": str(error),
                "error_type": "llm",
                "phase": "error"
            }
    
    async def _search_node(self, state: SearchState, config: Optional[Dict] = None) -> Dict[str, Any]:
        """Search phase - execute searches with multiple providers."""
        search_queries = state.get("search_queries", [])
        current_index = state.get("current_search_index", 0)
        
        if current_index >= len(search_queries):
            return {"phase": "scrape"}
        
        search_query = search_queries[current_index]
        
        try:
            results = await self._search_with_tavily(search_query)
            
            if not results:
                results = await self._search_with_perplexity(search_query)
            
            new_sources = []
            for result in results:
                source = Source(
                    url=result.get("url", ""),
                    title=result.get("title", ""),
                    content=result.get("content", ""),
                    quality=0.0
                )
                
                if source.content:
                    source.quality = self._score_content(source.content, state["query"])
                    
                    if len(source.content) > SEARCH_CONFIG["MIN_CONTENT_LENGTH"]:
                        summary = await self._summarize_content(source.content, search_query)
                        if summary and "no specific" not in summary.lower():
                            source.summary = summary
                
                new_sources.append(source)
            
            existing_sources = state.get("sources", [])
            source_map = {s.url: s for s in existing_sources}
            
            for source in new_sources:
                source_map[source.url] = source
            
            return {
                "sources": list(source_map.values()),
                "current_search_index": current_index + 1
            }
            
        except Exception:
            return {
                "current_search_index": current_index + 1,
                "error_type": "search"
            }
    
    async def _scrape_node(self, state: SearchState, config: Optional[Dict] = None) -> Dict[str, Any]:
        """Scraping phase - gather additional content from sources."""
        sources = state.get("sources", [])
        sources_to_scrape = [
            s for s in sources 
            if not s.content or len(s.content) < SEARCH_CONFIG["MIN_CONTENT_LENGTH"]
        ]
        
        new_scraped_sources = []
        
        sources_with_content = [
            s for s in sources 
            if s.content and len(s.content) >= SEARCH_CONFIG["MIN_CONTENT_LENGTH"]
        ]
        new_scraped_sources.extend(sources_with_content)
        
        max_scrape = min(len(sources_to_scrape), SEARCH_CONFIG["MAX_SOURCES_TO_SCRAPE"])
        
        for i in range(max_scrape):
            source = sources_to_scrape[i]
            
            try:
                scraped = await self.perplexity_client.scrape_url(source.url)
                
                if scraped.get("success") and scraped.get("content"):
                    enriched_source = Source(
                        url=source.url,
                        title=scraped.get("title", source.title),
                        content=scraped["content"],
                        quality=self._score_content(scraped["content"], state["query"])
                    )
                    
                    summary = await self._summarize_content(scraped["content"], state["query"])
                    if summary:
                        enriched_source.summary = summary
                    
                    new_scraped_sources.append(enriched_source)
                    
            except Exception:
                continue
        
        return {
            "scraped_sources": new_scraped_sources,
            "phase": "analyzing"
        }
    
    async def _analyze_node(self, state: SearchState, config: Optional[Dict] = None) -> Dict[str, Any]:
        """Analysis phase - process and score content relevance."""
        source_map = {}
        
        for source in state.get("sources", []):
            source_map[source.url] = source
        
        for source in state.get("scraped_sources", []):
            source_map[source.url] = source
        
        all_sources = list(source_map.values())
        
        sub_queries = state.get("sub_queries")
        if sub_queries:
            updated_sub_queries = await self._check_answers_in_sources(sub_queries, all_sources)
            
            answered_count = sum(1 for sq in updated_sub_queries if sq.answered)
            total_questions = len(updated_sub_queries)
            search_attempt = state.get("search_attempt", 0) + 1
            
            partial_answers = [sq for sq in updated_sub_queries if sq.confidence >= 0.3]
            has_partial_info = len(partial_answers) > answered_count
            
            if (answered_count < total_questions and 
                search_attempt < SEARCH_CONFIG["MAX_SEARCH_ATTEMPTS"] and
                not (has_partial_info and search_attempt >= 2)):
                
                return {
                    "sources": all_sources,
                    "sub_queries": updated_sub_queries,
                    "search_attempt": search_attempt,
                    "phase": "planning"  # Go back to planning for retry
                }
        
        try:
            source_dicts = [
                {
                    "title": s.title,
                    "url": s.url,
                    "content": s.content or "",
                    "score": s.quality or 0.0,
                    "published_date": datetime.now().isoformat()
                }
                for s in all_sources
            ]
            
            processed_sources = await self.context_processor.process_sources(
                state["query"],
                source_dicts,
                state.get("search_queries", [])
            )
            
            return {
                "sources": all_sources,
                "processed_sources": processed_sources,
                "sub_queries": sub_queries,
                "search_attempt": search_attempt if sub_queries else 0,
                "phase": "synthesizing"
            }
            
        except Exception:
            return {
                "sources": all_sources,
                "processed_sources": all_sources,
                "sub_queries": sub_queries,
                "search_attempt": search_attempt if sub_queries else 0,
                "phase": "synthesizing"
            }
    
    async def _synthesize_node(self, state: SearchState, config: Optional[Dict] = None) -> Dict[str, Any]:
        """Synthesis phase - generate comprehensive streaming answer."""
        try:
            sources_to_use = state.get("processed_sources") or state.get("sources", [])
            
            answer = await self._generate_streaming_answer(
                state["query"],
                sources_to_use,
                lambda chunk: None,  # Streaming callback handled by endpoint
                state.get("context")
            )
            
            follow_up_questions = await self._generate_follow_up_questions(
                state["query"],
                answer,
                sources_to_use,
                state.get("context")
            )
            
            return {
                "final_answer": answer,
                "follow_up_questions": follow_up_questions,
                "phase": "complete"
            }
            
        except Exception as error:
            return {
                "error": str(error),
                "error_type": "llm",
                "phase": "error"
            }
    
    async def _handle_error_node(self, state: SearchState, config: Optional[Dict] = None) -> Dict[str, Any]:
        """Error handling node with retry logic."""
        retry_count = state.get("retry_count", 0)
        max_retries = state.get("max_retries", SEARCH_CONFIG["MAX_RETRIES"])
        
        if retry_count < max_retries:
            error_type = state.get("error_type", "unknown")
            retry_phase = "searching" if error_type == "search" else "understanding"
            
            return {
                "retry_count": retry_count + 1,
                "phase": retry_phase,
                "error": None,
                "error_type": None
            }
        
        return {"phase": "error"}
    
    async def _complete_node(self, state: SearchState, config: Optional[Dict] = None) -> Dict[str, Any]:
        """Complete phase - finalize results."""
        return {"phase": "complete"}
    
    async def search(
        self,
        query: str,
        context: Optional[List[Dict[str, str]]] = None,
        event_callback: Optional[Callable[[SearchEvent], None]] = None
    ) -> Dict[str, Any]:
        """
        Execute the advanced search workflow.
        
        Args:
            query: The search query
            context: Optional conversation context
            event_callback: Optional callback for progress events
            
        Returns:
            Search results with answer and sources
        """
        initial_state: SearchState = {
            "query": query,
            "context": context,
            "current_search_index": 0,
            "search_attempt": 0,
            "sources": [],
            "scraped_sources": [],
            "phase": "understanding",
            "retry_count": 0,
            "max_retries": SEARCH_CONFIG["MAX_RETRIES"]
        }
        
        config = {
            "configurable": {
                "event_callback": event_callback
            }
        } if event_callback else {}
        
        result = await self.graph.ainvoke(initial_state, config)
        
        return {
            "answer": result.get("final_answer", ""),
            "sources": result.get("sources", []),
            "follow_up_questions": result.get("follow_up_questions", []),
            "phase": result.get("phase", "complete")
        }
    
    
    def _get_current_date_context(self) -> str:
        """Get current date context for queries."""
        now = datetime.now()
        return f"Today is {now.strftime('%A, %B %d, %Y')}. Current time: {now.strftime('%I:%M %p')} UTC."
    
    async def _analyze_query(self, query: str, context: Optional[List[Dict[str, str]]] = None) -> str:
        """Analyze the user query to understand intent."""
        context_str = ""
        if context:
            context_str = "\n".join([
                f"User: {c.get('query', '')}\nAssistant: {c.get('response', '')}"
                for c in context[-3:]  # Last 3 exchanges
            ])
        
        messages = [
            SystemMessage(content=f"""You are analyzing a user's search query to understand their intent and information needs.

{self._get_current_date_context()}

Previous conversation context:
{context_str}

Analyze the query and provide:
1. The main topic or subject
2. What specific information they're looking for
3. The type of answer they expect (factual, explanatory, comparative, etc.)
4. Any time-sensitive aspects
5. The level of detail needed

Be concise but thorough in your analysis."""),
            HumanMessage(content=f"Query to analyze: {query}")
        ]
        
        response = await self.llm.ainvoke(messages)
        return response.content
    
    async def _extract_sub_queries(self, query: str) -> List[Dict[str, str]]:
        """Extract sub-queries from the main query."""
        messages = [
            SystemMessage(content=f"""Break down this search query into 2-4 focused sub-questions that would help provide a comprehensive answer.

{self._get_current_date_context()}

For each sub-question, provide:
1. A clear, specific question
2. An optimized search query for that question

Format as JSON array with objects containing 'question' and 'searchQuery' fields.

Example:
[
  {{"question": "What is X?", "searchQuery": "X definition explanation"}},
  {{"question": "How does X work?", "searchQuery": "X mechanism process how it works"}}
]"""),
            HumanMessage(content=query)
        ]
        
        response = await self.llm.ainvoke(messages)
        try:
            return json.loads(response.content)
        except:
            return [{"question": query, "searchQuery": query}]
    
    async def _check_answers_in_sources(
        self, 
        sub_queries: List[SubQuery], 
        sources: List[Source]
    ) -> List[SubQuery]:
        """Check which sub-queries are answered by the sources."""
        updated_queries = []
        
        for sq in sub_queries:
            source_texts = []
            for source in sources[:SEARCH_CONFIG["MAX_SOURCES_TO_CHECK"]]:
                if source.summary:
                    source_texts.append(f"Source: {source.title}\nContent: {source.summary}")
                elif source.content:
                    preview = source.content[:1000]
                    source_texts.append(f"Source: {source.title}\nContent: {preview}")
            
            if not source_texts:
                updated_queries.append(sq)
                continue
            
            messages = [
                SystemMessage(content=f"""Analyze whether the following question can be answered using the provided sources.

Question: {sq.question}

Sources:
{chr(10).join(source_texts)}

Respond with a JSON object containing:
- "answered": boolean (true if the question can be reasonably answered)
- "confidence": number between 0-1 (how confident you are in the answer)
- "reasoning": brief explanation

Be conservative - only mark as answered if there's substantial relevant information."""),
                HumanMessage(content="Analyze the question against the sources.")
            ]
            
            try:
                response = await self.llm.ainvoke(messages)
                result = json.loads(response.content)
                
                sq.answered = result.get("answered", False)
                sq.confidence = result.get("confidence", 0.0)
                
            except:
                pass
            
            updated_queries.append(sq)
        
        return updated_queries
    
    async def _generate_alternative_search_queries(
        self, 
        sub_queries: List[SubQuery], 
        attempt: int
    ) -> List[str]:
        """Generate alternative search queries for retry attempts."""
        problematic_queries = [
            sq.search_query for sq in sub_queries 
            if not sq.answered or sq.confidence < SEARCH_CONFIG["MIN_ANSWER_CONFIDENCE"]
        ]
        
        alternative_approaches = [
            "Use more specific technical terms",
            "Try broader conceptual searches", 
            "Focus on recent developments",
            "Search for academic or research sources"
        ]
        
        approach = alternative_approaches[min(attempt - 1, len(alternative_approaches) - 1)]
        
        messages = [
            SystemMessage(content=f"""Generate alternative search queries for these unsuccessful searches.

Previous queries that didn't yield good results:
{chr(10).join(problematic_queries)}

Strategy for attempt #{attempt}: {approach}

Generate {len(problematic_queries)} alternative search queries that are more likely to find relevant information.
Return as a JSON array of strings."""),
            HumanMessage(content="Generate alternative search queries.")
        ]
        
        try:
            response = await self.llm.ainvoke(messages)
            queries = json.loads(response.content)
            return queries if isinstance(queries, list) else problematic_queries
        except:
            return problematic_queries
    
    def _score_content(self, content: str, query: str) -> float:
        """Score content relevance to query."""
        if not content or not query:
            return 0.0
        
        query_words = set(query.lower().split())
        content_words = set(content.lower().split())
        
        overlap = len(query_words.intersection(content_words))
        total_query_words = len(query_words)
        
        if total_query_words == 0:
            return 0.0
        
        base_score = overlap / total_query_words
        length_factor = min(len(content) / 1000, 1.0)  # Favor longer content up to 1000 chars
        
        return base_score * length_factor
    
    async def _summarize_content(self, content: str, query: str) -> Optional[str]:
        """Summarize content for relevance to query."""
        if len(content) < SEARCH_CONFIG["MIN_CONTENT_LENGTH"]:
            return None
        
        messages = [
            SystemMessage(content=f"""Summarize this content focusing on information relevant to the query: "{query}"

Extract key facts, data, and insights that directly relate to the query.
Keep the summary concise but informative (2-3 sentences).
If the content has little relevance to the query, say "No specific information found about [query topic]"."""),
            HumanMessage(content=content[:2000])  # Limit content length
        ]
        
        try:
            response = await self.llm.ainvoke(messages)
            return response.content.strip()
        except:
            return None
    
    async def _generate_streaming_answer(
        self,
        query: str,
        sources: List[Any],
        chunk_callback: Callable[[str], None],
        context: Optional[List[Dict[str, str]]] = None
    ) -> str:
        """Generate streaming answer from processed sources."""
        sources_text = ""
        for i, source in enumerate(sources[:10], 1):
            if hasattr(source, 'content'):
                content = source.content
            elif isinstance(source, dict):
                content = source.get('content', '')
            else:
                content = str(source)
            
            if content:
                title = getattr(source, 'title', '') or source.get('title', '') if isinstance(source, dict) else f"Source {i}"
                sources_text += f"\n\nSource {i} - {title}:\n{content[:1000]}"
        
        context_str = ""
        if context:
            context_str = "\n".join([
                f"User: {c.get('query', '')}\nAssistant: {c.get('response', '')}"
                for c in context[-2:]  # Last 2 exchanges
            ])
        
        messages = [
            SystemMessage(content=f"""You are a helpful research assistant. Provide a comprehensive, well-structured answer based on the provided sources.

{self._get_current_date_context()}

Previous conversation:
{context_str}

Guidelines:
- Use information from the sources to answer the question thoroughly
- Cite sources naturally in your response
- If sources conflict, acknowledge different perspectives
- Be factual and objective
- Structure your response clearly with proper paragraphs
- If the sources don't fully answer the question, acknowledge limitations

Sources:{sources_text}"""),
            HumanMessage(content=f"Question: {query}")
        ]
        
        try:
            response = await self.streaming_llm.ainvoke(messages)
            return response.content
        except Exception as e:
            return f"I apologize, but I encountered an error while generating the response: {str(e)}"
    
    async def _generate_follow_up_questions(
        self,
        query: str,
        answer: str,
        sources: List[Any],
        context: Optional[List[Dict[str, str]]] = None
    ) -> List[str]:
        """Generate relevant follow-up questions."""
        context_str = ""
        if context:
            context_str = "\n".join([
                f"User: {c.get('query', '')}"
                for c in context[-3:]  # Last 3 queries
            ])
        
        messages = [
            SystemMessage(content=f"""Based on the user's question and the answer provided, generate 2-3 relevant follow-up questions that would naturally extend the conversation.

Previous questions:
{context_str}

The follow-up questions should:
- Build on the current answer
- Explore related aspects not fully covered
- Be specific and actionable
- Avoid repeating previous questions

Return as a JSON array of strings."""),
            HumanMessage(content=f"Original question: {query}\n\nAnswer provided: {answer[:500]}...")
        ]
        
        try:
            response = await self.llm.ainvoke(messages)
            questions = json.loads(response.content)
            return questions if isinstance(questions, list) else []
        except:
            return []
    
    async def _search_with_tavily(self, query: str) -> List[Dict[str, Any]]:
        """Search using Tavily API."""
        try:
            response = self.tavily_client.search(
                query=query,
                max_results=SEARCH_CONFIG["MAX_SOURCES_PER_SEARCH"],
                include_answer=False,
                include_raw_content=True
            )
            
            results = []
            for result in response.get("results", []):
                results.append({
                    "url": result.get("url", ""),
                    "title": result.get("title", ""),
                    "content": result.get("raw_content", result.get("content", "")),
                    "score": result.get("score", 0.0)
                })
            
            return results
        except Exception:
            return []
    
    async def _search_with_perplexity(self, query: str) -> List[Dict[str, Any]]:
        """Search using Perplexity API as fallback."""
        try:
            response = await self.perplexity_client.search(
                query, 
                limit=SEARCH_CONFIG["MAX_SOURCES_PER_SEARCH"]
            )
            
            if response.get("success"):
                return response.get("results", [])
            return []
        except Exception:
            return []
