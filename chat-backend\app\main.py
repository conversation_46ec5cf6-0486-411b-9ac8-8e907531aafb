from fastapi import <PERSON><PERSON><PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import StreamingResponse
from pydantic import BaseModel
from typing import List, Dict, Any, Optional
import os
import json
import asyncio
from dotenv import load_dotenv
from langgraph.graph import StateGraph, END
from langgraph.config import get_stream_writer
from langchain_openai import Chat<PERSON>penA<PERSON>
from langchain_core.messages import HumanMessage, AIMessage, BaseMessage, SystemMessage
from typing_extensions import TypedDict
import uuid
from datetime import datetime
from tavily import TavilyClient
import re

from .advanced_search_engine import AdvancedSearchEngine, SearchEvent

load_dotenv()

app = FastAPI()

# Disable CORS. Do not remove this for full-stack development.
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Allows all origins
    allow_credentials=True,
    allow_methods=["*"],  # Allows all methods
    allow_headers=["*"],  # Allows all headers
)

chat_sessions: Dict[str, List[BaseMessage]] = {}

class ChatMessage(BaseModel):
    message: str
    session_id: str | None = None

class SearchResult(BaseModel):
    title: str
    url: str
    content: str

class ChatResponse(BaseModel):
    response: str
    session_id: str
    search_performed: bool = False
    search_query: Optional[str] = None
    search_results: List[SearchResult] = []

class ChatHistory(BaseModel):
    messages: List[Dict[str, Any]]
    session_id: str

class ChatState(TypedDict):
    messages: List[BaseMessage]
    session_id: str
    search_performed: bool
    search_query: Optional[str]
    search_results: List[Dict[str, Any]]
    needs_search: bool

llm = ChatOpenAI(
    model="gpt-3.5-turbo",
    temperature=0.7,
    api_key=os.getenv("OPENAI_API_KEY")
)

tavily_client = TavilyClient(api_key=os.getenv("TAVILY_API_KEY"))

advanced_search_engine = AdvancedSearchEngine()

def should_search(state: ChatState) -> str:
    """Use LLM to determine if web search is needed"""
    if state.get("search_performed", False):
        return "chat"
    
    last_message = state["messages"][-1].content
    
    # Use LLM to decide if search is needed
    decision_prompt = f"""
    Analyze this user message and determine if it requires current/real-time information that would need a web search.
    
    User message: "{last_message}"
    
    Return "SEARCH" if the query needs current information (stock prices, news, weather, recent events, etc.)
    Return "CHAT" if it can be answered with general knowledge (basic facts, explanations, math, etc.)
    
    Examples:
    - "What's NVIDIA's current market cap?" -> SEARCH (needs current data)
    - "Where is Paris?" -> CHAT (basic geography)
    - "Latest AI news" -> SEARCH (needs current info)
    - "Explain quantum physics" -> CHAT (general knowledge)
    
    Response:"""
    
    decision = llm.invoke([HumanMessage(content=decision_prompt)]).content.strip()
    
    if "SEARCH" in decision.upper():
        return "search"
    else:
        return "chat"

def tavily_search_node(state: ChatState) -> ChatState:
    """Perform web search using Tavily"""
    try:
        writer = get_stream_writer()
        writer({"status": "Calling Tavily...", "step": "search_start"})
        
        user_query = state["messages"][-1].content
        
        search_results = tavily_client.search(
            query=user_query,
            search_depth="basic",
            max_results=4
        )
        
        results_list = []
        for result in search_results.get("results", []):
            results_list.append({
                "title": result.get("title", ""),
                "url": result.get("url", ""),
                "content": result.get("content", "")
            })
        
        writer({"status": f"Found {len(results_list)} sources", "step": "search_complete", "results_count": len(results_list)})
        
        return {
            "messages": state["messages"],
            "session_id": state["session_id"],
            "search_performed": True,
            "search_query": user_query,
            "search_results": results_list,
            "needs_search": False
        }
    except Exception as e:
        writer = get_stream_writer()
        writer({"status": "Search failed", "step": "search_error"})
        return {
            "messages": state["messages"],
            "session_id": state["session_id"],
            "search_performed": False,
            "search_query": None,
            "search_results": [],
            "needs_search": False
        }

def chat_node(state: ChatState) -> ChatState:
    """Process the chat messages and generate AI response"""
    writer = get_stream_writer()
    writer({"status": "AI is thinking...", "step": "chat_start"})
    
    messages = state["messages"].copy()
    
    if state.get("search_performed", False) and state.get("search_results"):
        writer({"status": "Processing search results...", "step": "context_processing"})
        search_context = "\n\nBased on recent web search results:\n"
        for i, result in enumerate(state["search_results"], 1):
            search_context += f"{i}. {result['title']}\n   {result['content'][:200]}...\n   Source: {result['url']}\n\n"
        
        context_message = SystemMessage(content=f"You have access to current web search results. Use this information to provide an accurate and up-to-date response.{search_context}")
        messages.insert(-1, context_message)
    
    writer({"status": "Generating response...", "step": "llm_generating"})
    
    full_response = ""
    for chunk in llm.stream(messages):
        if chunk.content:
            full_response += chunk.content
            writer({"type": "content", "data": chunk.content})
    
    ai_message = AIMessage(content=full_response)
    messages.append(ai_message)
    
    writer({"status": "Response complete", "step": "chat_complete"})
    
    return {
        "messages": messages,
        "session_id": state["session_id"],
        "search_performed": state.get("search_performed", False),
        "search_query": state.get("search_query"),
        "search_results": state.get("search_results", []),
        "needs_search": False
    }

workflow = StateGraph(ChatState)
workflow.add_node("chat", chat_node)
workflow.add_node("search", tavily_search_node)

workflow.set_entry_point("chat")
workflow.add_conditional_edges(
    "chat",
    should_search,
    {
        "search": "search",
        "chat": END
    }
)
workflow.add_edge("search", END)

chat_graph = workflow.compile()

@app.get("/healthz")
async def healthz():
    return {"status": "ok"}

@app.post("/chat", response_model=ChatResponse)
async def chat_endpoint(chat_message: ChatMessage):
    """Send a message and get AI response"""
    try:
        session_id = chat_message.session_id or str(uuid.uuid4())
        
        if session_id not in chat_sessions:
            chat_sessions[session_id] = []
        
        user_message = HumanMessage(content=chat_message.message)
        chat_sessions[session_id].append(user_message)
        
        state = ChatState(
            messages=chat_sessions[session_id].copy(),
            session_id=session_id,
            search_performed=False,
            search_query=None,
            search_results=[],
            needs_search=False
        )
        
        result = chat_graph.invoke(state)
        
        chat_sessions[session_id] = result["messages"]
        
        ai_response = result["messages"][-1].content
        
        search_results = []
        if result.get("search_results"):
            search_results = [
                SearchResult(
                    title=r["title"],
                    url=r["url"],
                    content=r["content"]
                ) for r in result["search_results"]
            ]
        
        return ChatResponse(
            response=ai_response,
            session_id=session_id,
            search_performed=result.get("search_performed", False),
            search_query=result.get("search_query"),
            search_results=search_results
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Chat error: {str(e)}")

@app.get("/chat/history/{session_id}", response_model=ChatHistory)
async def get_chat_history(session_id: str):
    """Get chat history for a session"""
    if session_id not in chat_sessions:
        raise HTTPException(status_code=404, detail="Session not found")
    
    messages = []
    for msg in chat_sessions[session_id]:
        if isinstance(msg, HumanMessage):
            messages.append({"role": "user", "content": msg.content, "timestamp": datetime.now().isoformat()})
        elif isinstance(msg, AIMessage):
            messages.append({"role": "assistant", "content": msg.content, "timestamp": datetime.now().isoformat()})
    
    return ChatHistory(messages=messages, session_id=session_id)

@app.delete("/chat/history/{session_id}")
async def clear_chat_history(session_id: str):
    """Clear chat history for a session"""
    if session_id in chat_sessions:
        del chat_sessions[session_id]
        return {"message": "Chat history cleared"}
    else:
        raise HTTPException(status_code=404, detail="Session not found")

@app.post("/chat/stream")
async def chat_stream_endpoint(chat_message: ChatMessage):
    """Stream chat responses with progressive updates"""
    async def generate_stream():
        try:
            session_id = chat_message.session_id or str(uuid.uuid4())
            
            if session_id not in chat_sessions:
                chat_sessions[session_id] = []
            
            user_message = HumanMessage(content=chat_message.message)
            chat_sessions[session_id].append(user_message)
            
            state = ChatState(
                messages=chat_sessions[session_id].copy(),
                session_id=session_id,
                search_performed=False,
                search_query=None,
                search_results=[],
                needs_search=False
            )
            
            final_result = None
            async for chunk in chat_graph.astream(state, stream_mode=["updates", "custom"]):
                if isinstance(chunk, tuple):
                    mode, data = chunk
                    if mode == "custom":
                        if data.get("type") == "content":
                            yield f"data: {json.dumps({'type': 'content', 'data': data.get('data')})}\n\n"
                        else:
                            yield f"data: {json.dumps({'type': 'status', 'data': data})}\n\n"
                    elif mode == "updates":
                        for node_name, node_result in data.items():
                            serializable_result = {}
                            for key, value in node_result.items():
                                if key == "messages":
                                    serializable_result[key] = [
                                        {"role": "user" if isinstance(msg, HumanMessage) else "assistant", 
                                         "content": msg.content} 
                                        for msg in value
                                    ]
                                else:
                                    serializable_result[key] = value
                            yield f"data: {json.dumps({'type': 'node_complete', 'node': node_name, 'data': serializable_result})}\n\n"
                        final_result = data
                else:
                    if 'search' in chunk:
                        serializable_data = {}
                        for key, value in chunk['search'].items():
                            if key == "messages":
                                serializable_data[key] = [
                                    {"role": "user" if isinstance(msg, HumanMessage) else "assistant", 
                                     "content": msg.content} 
                                    for msg in value
                                ]
                            else:
                                serializable_data[key] = value
                        yield f"data: {json.dumps({'type': 'node_complete', 'node': 'search', 'data': serializable_data})}\n\n"
                        final_result = chunk
                    elif 'chat' in chunk:
                        serializable_data = {}
                        for key, value in chunk['chat'].items():
                            if key == "messages":
                                serializable_data[key] = [
                                    {"role": "user" if isinstance(msg, HumanMessage) else "assistant", 
                                     "content": msg.content} 
                                    for msg in value
                                ]
                            else:
                                serializable_data[key] = value
                        yield f"data: {json.dumps({'type': 'node_complete', 'node': 'chat', 'data': serializable_data})}\n\n"
                        final_result = chunk
            
            if final_result:
                if 'chat' in final_result:
                    chat_sessions[session_id] = final_result['chat']['messages']
                elif 'search' in final_result:
                    pass
            
            ai_response = chat_sessions[session_id][-1].content if chat_sessions[session_id] else "No response generated"
            
            search_results = []
            search_performed = False
            search_query = None
            
            if final_result:
                for node_data in final_result.values():
                    if node_data.get("search_results"):
                        search_results = [
                            SearchResult(
                                title=r["title"],
                                url=r["url"],
                                content=r["content"]
                            ) for r in node_data["search_results"]
                        ]
                        search_performed = node_data.get("search_performed", False)
                        search_query = node_data.get("search_query")
                        break
            
            final_response = ChatResponse(
                response=ai_response,
                session_id=session_id,
                search_performed=search_performed,
                search_query=search_query,
                search_results=search_results
            )
            
            yield f"data: {json.dumps({'type': 'final', 'data': final_response.model_dump()})}\n\n"
            yield f"data: [DONE]\n\n"
            
        except Exception as e:
            error_response = {"type": "error", "message": f"Chat error: {str(e)}"}
            yield f"data: {json.dumps(error_response)}\n\n"
    
    return StreamingResponse(
        generate_stream(),
        media_type="text/plain",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Content-Type": "text/event-stream",
        }
    )

@app.post("/chat/advanced-stream")
async def advanced_chat_stream_endpoint(chat_message: ChatMessage):
    """Stream chat responses using the advanced LangGraph search engine"""
    async def generate_advanced_stream():
        try:
            session_id = chat_message.session_id or str(uuid.uuid4())
            
            if session_id not in chat_sessions:
                chat_sessions[session_id] = []
            
            context = []
            messages = chat_sessions[session_id]
            for i in range(0, len(messages) - 1, 2):
                if i + 1 < len(messages):
                    user_msg = messages[i]
                    ai_msg = messages[i + 1]
                    if isinstance(user_msg, HumanMessage) and isinstance(ai_msg, AIMessage):
                        context.append({
                            "query": user_msg.content,
                            "response": ai_msg.content
                        })
            
            def event_callback(event: SearchEvent):
                event_data = {
                    "type": event.type,
                    "phase": event.phase,
                    "message": event.message,
                    "query": event.query,
                    "index": event.index,
                    "total": event.total,
                    "url": event.url,
                    "title": event.title,
                    "stage": event.stage,
                    "summary": event.summary,
                    "chunk": event.chunk,
                    "content": event.content,
                    "follow_up_questions": event.follow_up_questions,
                    "error": event.error,
                    "error_type": event.error_type
                }
                
                event_data = {k: v for k, v in event_data.items() if v is not None}
                
                return f"data: {json.dumps({'type': 'search_event', 'data': event_data})}\n\n"
            
            events_queue = []
            
            def queue_event(event: SearchEvent):
                events_queue.append(event_callback(event))
            
            result = await advanced_search_engine.search(
                query=chat_message.message,
                context=context[-3:] if context else None,  # Last 3 exchanges
                event_callback=queue_event
            )
            
            for event_str in events_queue:
                yield event_str
            
            user_message = HumanMessage(content=chat_message.message)
            chat_sessions[session_id].append(user_message)
            
            ai_response = result.get("answer", "I apologize, but I couldn't generate a response.")
            ai_message = AIMessage(content=ai_response)
            chat_sessions[session_id].append(ai_message)
            
            search_results = []
            for source in result.get("sources", []):
                if hasattr(source, 'url'):
                    search_results.append(SearchResult(
                        title=source.title or "Untitled",
                        url=source.url,
                        content=source.content or source.summary or ""
                    ))
                elif isinstance(source, dict):
                    search_results.append(SearchResult(
                        title=source.get("title", "Untitled"),
                        url=source.get("url", ""),
                        content=source.get("content", source.get("summary", ""))
                    ))
            
            final_response = ChatResponse(
                response=ai_response,
                session_id=session_id,
                search_performed=len(search_results) > 0,
                search_query=chat_message.message,
                search_results=search_results
            )
            
            if result.get("follow_up_questions"):
                final_data = final_response.model_dump()
                final_data["follow_up_questions"] = result["follow_up_questions"]
                yield f"data: {json.dumps({'type': 'final', 'data': final_data})}\n\n"
            else:
                yield f"data: {json.dumps({'type': 'final', 'data': final_response.model_dump()})}\n\n"
            
            yield f"data: [DONE]\n\n"
            
        except Exception as e:
            error_response = {"type": "error", "message": f"Advanced search error: {str(e)}"}
            yield f"data: {json.dumps(error_response)}\n\n"
    
    return StreamingResponse(
        generate_advanced_stream(),
        media_type="text/plain",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Content-Type": "text/event-stream",
        }
    )

@app.get("/chat/sessions")
async def get_active_sessions():
    """Get list of active chat sessions"""
    return {"sessions": list(chat_sessions.keys())}
