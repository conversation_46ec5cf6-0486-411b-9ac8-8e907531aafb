"""
Perplexity API client for web search functionality.
Alternative to Firecrawl, using Perplexity's search API.
"""

import asyncio
import aiohttp
import json
from typing import List, Dict, Any, Optional
from datetime import datetime


class PerplexityClient:
    """
    Client for interacting with the Perplexity API for web search.
    Provides search functionality as an alternative to Firecrawl.
    """
    
    def __init__(self, api_key: str):
        self.api_key = api_key
        self.base_url = "https://api.perplexity.ai"
        self.headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json"
        }
    
    async def search(self, query: str, limit: int = 10) -> Dict[str, Any]:
        """
        Search using Perplexity API and return results in a format compatible with the existing system.
        
        Args:
            query: The search query string
            limit: Maximum number of results to return (default: 10)
            
        Returns:
            Dict containing search results in the format expected by the search engine
        """
        try:
            async with aiohttp.ClientSession() as session:
                payload = {
                    "model": "llama-3.1-sonar-small-128k-online",
                    "messages": [
                        {
                            "role": "system",
                            "content": "You are a helpful search assistant. Provide comprehensive information about the query with relevant sources."
                        },
                        {
                            "role": "user", 
                            "content": f"Search for: {query}"
                        }
                    ],
                    "max_tokens": 1000,
                    "temperature": 0.2,
                    "top_p": 0.9,
                    "return_citations": True,
                    "search_domain_filter": ["perplexity.ai"],
                    "return_images": False,
                    "return_related_questions": True,
                    "search_recency_filter": "month",
                    "top_k": limit
                }
                
                async with session.post(
                    f"{self.base_url}/chat/completions",
                    headers=self.headers,
                    json=payload,
                    timeout=aiohttp.ClientTimeout(total=30)
                ) as response:
                    if response.status == 200:
                        data = await response.json()
                        return self._format_search_results(data, query)
                    else:
                        error_text = await response.text()
                        return {
                            "success": False,
                            "error": f"Perplexity API error {response.status}: {error_text}",
                            "results": []
                        }
                        
        except asyncio.TimeoutError:
            return {
                "success": False,
                "error": "Search request timed out",
                "results": []
            }
        except Exception as e:
            return {
                "success": False,
                "error": f"Search failed: {str(e)}",
                "results": []
            }
    
    def _format_search_results(self, perplexity_response: Dict[str, Any], query: str) -> Dict[str, Any]:
        """
        Format Perplexity API response to match the expected search result format.
        
        Args:
            perplexity_response: Raw response from Perplexity API
            query: Original search query
            
        Returns:
            Formatted search results compatible with the existing system
        """
        try:
            results = []
            
            if "choices" in perplexity_response and len(perplexity_response["choices"]) > 0:
                choice = perplexity_response["choices"][0]
                content = choice.get("message", {}).get("content", "")
                
                citations = choice.get("citations", [])
                
                for i, citation in enumerate(citations[:10]):  # Limit to 10 results
                    result = {
                        "title": citation.get("title", f"Search Result {i+1}"),
                        "url": citation.get("url", ""),
                        "content": citation.get("text", content[:500] if i == 0 else ""),
                        "score": 1.0 - (i * 0.1),  # Decreasing relevance score
                        "published_date": citation.get("published_date", datetime.now().isoformat())
                    }
                    results.append(result)
                
                if not results and content:
                    results.append({
                        "title": f"Perplexity Search: {query}",
                        "url": "https://perplexity.ai",
                        "content": content[:1000],
                        "score": 1.0,
                        "published_date": datetime.now().isoformat()
                    })
            
            return {
                "success": True,
                "results": results,
                "query": query,
                "total_results": len(results)
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": f"Failed to format search results: {str(e)}",
                "results": []
            }
    
    async def scrape_url(self, url: str) -> Dict[str, Any]:
        """
        Scrape content from a URL. 
        Note: This is a simplified implementation since Perplexity doesn't provide direct scraping.
        For full scraping functionality, you might want to use a dedicated scraping service.
        
        Args:
            url: URL to scrape
            
        Returns:
            Dict containing scraped content or error information
        """
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(
                    url,
                    timeout=aiohttp.ClientTimeout(total=15),
                    headers={"User-Agent": "Mozilla/5.0 (compatible; PerplexityBot/1.0)"}
                ) as response:
                    if response.status == 200:
                        content = await response.text()
                        return {
                            "success": True,
                            "url": url,
                            "content": content[:5000],  # Limit content length
                            "title": self._extract_title_from_html(content),
                            "status_code": response.status
                        }
                    else:
                        return {
                            "success": False,
                            "error": f"HTTP {response.status}",
                            "url": url
                        }
        except Exception as e:
            return {
                "success": False,
                "error": f"Scraping failed: {str(e)}",
                "url": url
            }
    
    def _extract_title_from_html(self, html_content: str) -> str:
        """
        Extract title from HTML content using simple string parsing.
        
        Args:
            html_content: Raw HTML content
            
        Returns:
            Extracted title or default title
        """
        try:
            start = html_content.find("<title>")
            if start != -1:
                start += 7
                end = html_content.find("</title>", start)
                if end != -1:
                    return html_content[start:end].strip()
        except:
            pass
        return "Untitled Page"
