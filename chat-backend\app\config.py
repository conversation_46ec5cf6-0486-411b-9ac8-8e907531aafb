"""
Configuration settings for the advanced search engine.
Based on the TypeScript config.ts file.
"""

SEARCH_CONFIG = {
    "MAX_SEARCH_QUERIES": 4,        # Maximum number of search queries to generate
    "MAX_SOURCES_PER_SEARCH": 6,     # Maximum sources to return per search query
    "MAX_SOURCES_TO_SCRAPE": 6,      # Maximum sources to scrape for additional content
    
    "MIN_CONTENT_LENGTH": 100,       # Minimum content length to consider valid
    "SUMMARY_CHAR_LIMIT": 100,       # Character limit for source summaries
    "CONTEXT_PREVIEW_LENGTH": 500,   # Preview length for previous context
    "ANSWER_CHECK_PREVIEW": 2500,    # Content preview length for answer checking
    "MAX_SOURCES_TO_CHECK": 10,      # Maximum sources to check for answers
    
    "MAX_RETRIES": 2,                # Maximum retry attempts for failed operations
    "MAX_SEARCH_ATTEMPTS": 3,        # Maximum attempts to find answers via search
    "MIN_ANSWER_CONFIDENCE": 0.3,    # Minimum confidence (0-1) that a question was answered
    "EARLY_TERMINATION_CONFIDENCE": 0.8, # Confidence level to skip additional searches
    
    "SCRAPE_TIMEOUT": 15000,         # Timeout for scraping operations (ms)
    
    "SOURCE_ANIMATION_DELAY": 50,    # Delay between source animations (ms) - reduced from 150
    "PARALLEL_SUMMARY_GENERATION": True, # Generate summaries in parallel
}

MODEL_CONFIG = {
    "FAST_MODEL": "gpt-4o-mini",
    "QUALITY_MODEL": "gpt-4o",
    "TEMPERATURE": 0,
}

UI_CONFIG = {
    "ANIMATION_DURATION": 300,       # Default animation duration (ms)
    "SOURCE_FADE_DELAY": 50,         # Delay between source animations (ms)
    "MESSAGE_CYCLE_DELAY": 2000,     # Delay for cycling through messages (ms)
}
