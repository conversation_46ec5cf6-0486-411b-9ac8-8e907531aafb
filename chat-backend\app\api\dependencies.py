"""
FastAPI dependencies.
"""

from ..shared.utils import <PERSON><PERSON><PERSON><PERSON>
from ..workflows import SimpleChatWorkflow, AdvancedSearchWorkflow

# Global instances
session_manager = SessionManager()
simple_chat_workflow = SimpleChatWorkflow()
advanced_search_workflow = AdvancedSearchWorkflow()


def get_session_manager() -> SessionManager:
    """Get session manager dependency."""
    return session_manager


def get_simple_chat_workflow() -> SimpleChatWorkflow:
    """Get simple chat workflow dependency."""
    return simple_chat_workflow


def get_advanced_search_workflow() -> AdvancedSearchWorkflow:
    """Get advanced search workflow dependency."""
    return advanced_search_workflow
